{"imports": {"$store/": "./", "deco/": "https://denopkg.com/deco-cx/deco@1.52.6/", "apps/": "https://denopkg.com/deco-cx/apps@542ff8ac6ae7643fcdfdb84cfcd120d5e86624e3/", "$fresh/": "https://deno.land/x/fresh@1.6.3/", "preact": "https://esm.sh/preact@10.19.2", "preact/": "https://esm.sh/preact@10.19.2/", "preact-render-to-string": "https://esm.sh/*preact-render-to-string@6.2.2", "@preact/signals": "https://esm.sh/*@preact/signals@1.2.1", "@preact/signals-core": "https://esm.sh/*@preact/signals-core@1.5.0", "std/": "https://deno.land/std@0.190.0/", "partytown/": "https://denopkg.com/deco-cx/partytown@0.4.8/", "daisyui": "npm:daisyui@4.6.0"}, "tasks": {"start": "deno task bundle && deno run -A --unstable --env --watch=tailwind.css,sections/,functions/,loaders/,actions/,workflows/,accounts/,.env dev.ts", "gen": "deno run -A dev.ts --gen-only", "play": "USE_LOCAL_STORAGE_ONLY=true deno task start", "component": "deno eval 'import \"deco/scripts/component.ts\"'", "release": "deno eval 'import \"deco/scripts/release.ts\"'", "update": "deno run -Ar https://deco.cx/update", "check": "deno fmt && deno lint && deno check dev.ts main.ts", "install": "deno eval 'import \"deco/scripts/apps/install.ts\"'", "uninstall": "deno eval 'import \"deco/scripts/apps/uninstall.ts\"'", "bundle": "deno eval 'import \"deco/scripts/apps/bundle.ts\"' deco-sites/storefront", "cache_clean": "rm deno.lock; deno cache -r main.ts", "build": "deno run -A dev.ts build", "preview": "deno run -A main.ts"}, "githooks": {"pre-commit": "check"}, "exclude": ["node_modules", "static/", "README.md", "_fresh", "**/_fresh/*"], "lint": {"rules": {"tags": ["fresh", "recommended"]}}, "nodeModulesDir": true, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact"}}