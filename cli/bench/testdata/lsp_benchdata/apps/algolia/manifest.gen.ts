// DO NOT EDIT. This file is generated by deco.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $$$$$$$$$0 from "./actions/index/product.ts";
import * as $$$$$$$$$1 from "./actions/index/wait.ts";
import * as $$$$$$$$$2 from "./actions/setup.ts";
import * as $$$0 from "./loaders/product/list.ts";
import * as $$$1 from "./loaders/product/listingPage.ts";
import * as $$$2 from "./loaders/product/suggestions.ts";
import * as $$$$$$0 from "./sections/Analytics/Algolia.tsx";
import * as $$$$$$$$$$0 from "./workflows/index/product.ts";

const manifest = {
  "loaders": {
    "algolia/loaders/product/list.ts": $$$0,
    "algolia/loaders/product/listingPage.ts": $$$1,
    "algolia/loaders/product/suggestions.ts": $$$2,
  },
  "sections": {
    "algolia/sections/Analytics/Algolia.tsx": $$$$$$0,
  },
  "actions": {
    "algolia/actions/index/product.ts": $$$$$$$$$0,
    "algolia/actions/index/wait.ts": $$$$$$$$$1,
    "algolia/actions/setup.ts": $$$$$$$$$2,
  },
  "workflows": {
    "algolia/workflows/index/product.ts": $$$$$$$$$$0,
  },
  "name": "algolia",
  "baseUrl": import.meta.url,
};

export type Manifest = typeof manifest;

export default manifest;
