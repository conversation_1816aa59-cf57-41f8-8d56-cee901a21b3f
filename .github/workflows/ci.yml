# GENERATED BY ./ci.generate.ts -- DO NOT DIRECTLY EDIT

name: ci
permissions:
  contents: write
  id-token: write
on:
  push:
    branches:
      - main
    tags:
      - '*'
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
concurrency:
  group: '${{ github.workflow }}-${{ !contains(github.event.pull_request.labels.*.name, ''ci-test-flaky'') && github.head_ref || github.run_id }}'
  cancel-in-progress: true
jobs:
  pre_build:
    name: pre-build
    runs-on: ubuntu-latest
    outputs:
      skip_build: '${{ steps.check.outputs.skip_build }}'
    steps:
      - name: Configure git
        run: |-
          git config --global core.symlinks true
          git config --global fetch.parallel 32
        if: github.event.pull_request.draft == true
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 5
          submodules: false
        if: github.event.pull_request.draft == true
      - id: check
        if: 'github.event.pull_request.draft == true && (!contains(github.event.pull_request.labels.*.name, ''ci-draft''))'
        run: |-
          GIT_MESSAGE=$(git log --format=%s -n 1 ${{github.event.after}})
          echo Commit message: $GIT_MESSAGE
          echo $GIT_MESSAGE | grep '\[ci\]' || (echo 'Exiting due to draft PR. Commit with [ci] to bypass or add the ci-draft label.' ; echo 'skip_build=true' >> $GITHUB_OUTPUT)
  build:
    name: '${{ matrix.job }} ${{ matrix.profile }} ${{ matrix.os }}-${{ matrix.arch }}'
    needs:
      - pre_build
    if: '${{ needs.pre_build.outputs.skip_build != ''true'' }}'
    runs-on: '${{ matrix.runner }}'
    environment:
      name: '${{ (github.ref == ''refs/heads/main'' || startsWith(github.ref, ''refs/tags/'')) && ''build'' || '''' }}'
    timeout-minutes: 240
    defaults:
      run:
        shell: bash
    strategy:
      matrix:
        include:
          - os: macos
            arch: x86_64
            runner: macos-13
            job: test
            profile: debug
          - os: macos
            arch: x86_64
            runner: '${{ (!contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'')) && ''ubuntu-24.04'' || ''macos-13'' }}'
            job: test
            profile: release
            skip: '${{ !contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'') }}'
          - os: macos
            arch: aarch64
            runner: macos-14
            job: test
            profile: debug
          - os: macos
            arch: aarch64
            runner: '${{ (!contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'')) && ''ubuntu-24.04'' || github.repository == ''denoland/deno'' && (github.ref == ''refs/heads/main'' || startsWith(github.ref, ''refs/tags/'')) && ''ghcr.io/cirruslabs/macos-runner:sonoma'' || ''macos-14'' }}'
            job: test
            profile: release
            skip: '${{ !contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'') }}'
          - os: windows
            arch: x86_64
            runner: windows-2022
            job: test
            profile: debug
          - os: windows
            arch: x86_64
            runner: '${{ (!contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'')) && ''ubuntu-24.04'' || github.repository == ''denoland/deno'' && ''windows-2022-xl'' || ''windows-2022'' }}'
            job: test
            profile: release
            skip: '${{ !contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'') }}'
          - os: linux
            arch: x86_64
            runner: '${{ github.repository == ''denoland/deno'' && ''ubuntu-24.04-xl'' || ''ubuntu-24.04'' }}'
            job: test
            profile: release
            use_sysroot: true
            wpt: '${{ !startsWith(github.ref, ''refs/tags/'') }}'
          - os: linux
            arch: x86_64
            runner: '${{ (!contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'' && !contains(github.event.pull_request.labels.*.name, ''ci-bench''))) && ''ubuntu-24.04'' || github.repository == ''denoland/deno'' && ''ubuntu-24.04-xl'' || ''ubuntu-24.04'' }}'
            job: bench
            profile: release
            use_sysroot: true
            skip: '${{ !contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'' && !contains(github.event.pull_request.labels.*.name, ''ci-bench'')) }}'
          - os: linux
            arch: x86_64
            runner: ubuntu-24.04
            job: test
            profile: debug
            use_sysroot: true
          - os: linux
            arch: x86_64
            runner: ubuntu-24.04
            job: lint
            profile: debug
          - os: linux
            arch: aarch64
            runner: ubicloud-standard-16-arm
            job: test
            profile: debug
          - os: linux
            arch: aarch64
            runner: '${{ (!contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'')) && ''ubuntu-24.04'' || ''ubicloud-standard-16-arm'' }}'
            job: test
            profile: release
            use_sysroot: true
            skip: '${{ !contains(github.event.pull_request.labels.*.name, ''ci-full'') && (github.event_name == ''pull_request'') }}'
          - os: macos
            arch: x86_64
            runner: macos-13
            job: lint
            profile: debug
          - os: windows
            arch: x86_64
            runner: windows-2022
            job: lint
            profile: debug
      fail-fast: '${{ github.event_name == ''pull_request'' || (github.ref != ''refs/heads/main'' && !startsWith(github.ref, ''refs/tags/'')) }}'
    env:
      CARGO_TERM_COLOR: always
      RUST_BACKTRACE: full
      RUST_LIB_BACKTRACE: 0
    steps:
      - name: Configure git
        run: |-
          git config --global core.symlinks true
          git config --global fetch.parallel 32
        if: '!(matrix.skip)'
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 5
          submodules: false
        if: '!(matrix.skip)'
      - name: Clone submodule ./tests/util/std
        run: git submodule update --init --recursive --depth=1 -- ./tests/util/std
        if: '!(matrix.skip)'
      - name: Clone submodule ./tests/wpt/suite
        run: git submodule update --init --recursive --depth=1 -- ./tests/wpt/suite
        if: '!(matrix.skip) && (matrix.wpt)'
      - name: Clone submodule ./tests/node_compat/runner/suite
        run: git submodule update --init --recursive --depth=1 -- ./tests/node_compat/runner/suite
        if: '!(matrix.skip) && (matrix.job == ''lint'' && matrix.os == ''linux'')'
      - name: Clone submodule ./cli/bench/testdata/lsp_benchdata
        run: git submodule update --init --recursive --depth=1 -- ./cli/bench/testdata/lsp_benchdata
        if: '!(matrix.skip) && (matrix.job == ''bench'')'
      - name: 'Create source tarballs (release, linux)'
        if: |-
          !(matrix.skip) && (matrix.os == 'linux' &&
          matrix.profile == 'release' &&
          matrix.job == 'test' &&
          github.repository == 'denoland/deno' &&
          startsWith(github.ref, 'refs/tags/'))
        run: |-
          mkdir -p target/release
          tar --exclude=".git*" --exclude=target --exclude=third_party/prebuilt \
              -czvf target/release/deno_src.tar.gz -C .. deno
      - name: Cache Cargo home
        uses: cirruslabs/cache@v4
        with:
          path: |-
            ~/.cargo/.crates.toml
            ~/.cargo/.crates2.json
            ~/.cargo/bin
            ~/.cargo/registry/index
            ~/.cargo/registry/cache
            ~/.cargo/git/db
          key: '59-cargo-home-${{ matrix.os }}-${{ matrix.arch }}-${{ hashFiles(''Cargo.lock'') }}'
          restore-keys: '59-cargo-home-${{ matrix.os }}-${{ matrix.arch }}-'
        if: '!(matrix.skip)'
      - uses: dsherret/rust-toolchain-file@v1
        if: '!(matrix.skip)'
      - if: '!(matrix.skip) && (matrix.job == ''lint'' || matrix.job == ''test'' || matrix.job == ''bench'')'
        name: Install Deno
        uses: denoland/setup-deno@v2
        with:
          deno-version: v2.x
      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
        if: '!(matrix.skip) && (matrix.job != ''lint'' && (matrix.os != ''linux'' || matrix.arch != ''aarch64''))'
      - name: Remove unused versions of Python
        if: '!(matrix.skip) && (matrix.job != ''lint'' && (matrix.os != ''linux'' || matrix.arch != ''aarch64'') && (matrix.os == ''windows''))'
        shell: pwsh
        run: |-
          $env:PATH -split ";" |
            Where-Object { Test-Path "$_\python.exe" } |
            Select-Object -Skip 1 |
            ForEach-Object { Move-Item "$_" "$_.disabled" }
      - if: '!(matrix.skip) && (matrix.job == ''bench'' || matrix.job == ''test'')'
        name: Install Node
        uses: actions/setup-node@v4
        with:
          node-version: 18
      - if: |-
          !(matrix.skip) && (matrix.profile == 'release' &&
          matrix.job == 'test' &&
          github.repository == 'denoland/deno' &&
          (github.ref == 'refs/heads/main' ||
          startsWith(github.ref, 'refs/tags/')))
        name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          project_id: denoland
          credentials_json: '${{ secrets.GCP_SA_KEY }}'
          export_environment_variables: true
          create_credentials_file: true
      - name: Setup gcloud (unix)
        if: |-
          !(matrix.skip) && (matrix.os != 'windows' &&
          matrix.profile == 'release' &&
          matrix.job == 'test' &&
          github.repository == 'denoland/deno' &&
          (github.ref == 'refs/heads/main' ||
          startsWith(github.ref, 'refs/tags/')))
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: denoland
      - name: Setup gcloud (windows)
        if: |-
          !(matrix.skip) && (matrix.os == 'windows' &&
          matrix.profile == 'release' &&
          matrix.job == 'test' &&
          github.repository == 'denoland/deno' &&
          (github.ref == 'refs/heads/main' ||
          startsWith(github.ref, 'refs/tags/')))
        uses: google-github-actions/setup-gcloud@v2
        env:
          CLOUDSDK_PYTHON: '${{env.pythonLocation}}\python.exe'
        with:
          project_id: denoland
      - name: Configure canary build
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          github.ref == 'refs/heads/main')
        run: echo "DENO_CANARY=true" >> $GITHUB_ENV
      - if: '!(matrix.skip) && (matrix.use_sysroot)'
        name: Set up incremental LTO and sysroot build
        run: |-
          # Setting up sysroot
          export DEBIAN_FRONTEND=noninteractive
          # Avoid running man-db triggers, which sometimes takes several minutes
          # to complete.
          sudo apt-get -qq remove --purge -y man-db  > /dev/null 2> /dev/null
          # Remove older clang before we install
          sudo apt-get -qq remove   'clang-12*' 'clang-13*' 'clang-14*' 'clang-15*' 'clang-16*' 'clang-17*' 'clang-18*' 'llvm-12*' 'llvm-13*' 'llvm-14*' 'llvm-15*' 'llvm-16*' 'llvm-17*' 'llvm-18*' 'lld-12*' 'lld-13*' 'lld-14*' 'lld-15*' 'lld-16*' 'lld-17*' 'lld-18*' > /dev/null 2> /dev/null

          # Install clang-XXX, lld-XXX, and debootstrap.
          echo "deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-19 main" |
            sudo dd of=/etc/apt/sources.list.d/llvm-toolchain-jammy-19.list
          curl https://apt.llvm.org/llvm-snapshot.gpg.key |
            gpg --dearmor                                 |
          sudo dd of=/etc/apt/trusted.gpg.d/llvm-snapshot.gpg
          sudo apt-get update
          # this was unreliable sometimes, so try again if it fails
          sudo apt-get install --no-install-recommends clang-19 lld-19 clang-tools-19 clang-format-19 clang-tidy-19 || echo 'Failed. Trying again.' && sudo apt-get clean && sudo apt-get update && sudo apt-get install --no-install-recommends clang-19 lld-19 clang-tools-19 clang-format-19 clang-tidy-19
          # Fix alternatives
          (yes '' | sudo update-alternatives --force --all) > /dev/null 2> /dev/null || true

          clang-19 -c -o /tmp/memfd_create_shim.o tools/memfd_create_shim.c -fPIC

          echo "Decompressing sysroot..."
          wget -q https://github.com/denoland/deno_sysroot_build/releases/download/sysroot-20250207/sysroot-`uname -m`.tar.xz -O /tmp/sysroot.tar.xz
          cd /
          xzcat /tmp/sysroot.tar.xz | sudo tar -x
          sudo mount --rbind /dev /sysroot/dev
          sudo mount --rbind /sys /sysroot/sys
          sudo mount --rbind /home /sysroot/home
          sudo mount -t proc /proc /sysroot/proc
          cd

          echo "Done."

          # Configure the build environment. Both Rust and Clang will produce
          # llvm bitcode only, so we can use lld's incremental LTO support.

          # Load the sysroot's env vars
          echo "sysroot env:"
          cat /sysroot/.env
          . /sysroot/.env

          # Important notes:
          #   1. -ldl seems to be required to avoid a failure in FFI tests. This flag seems
          #      to be in the Rust default flags in the smoketest, so uncertain why we need
          #      to be explicit here.
          #   2. RUSTFLAGS and RUSTDOCFLAGS must be specified, otherwise the doctests fail
          #      to build because the object formats are not compatible.
          echo "
          CARGO_PROFILE_BENCH_INCREMENTAL=false
          CARGO_PROFILE_RELEASE_INCREMENTAL=false
          RUSTFLAGS<<__1
            -C linker-plugin-lto=true
            -C linker=clang-19
            -C link-arg=-fuse-ld=lld-19
            -C link-arg=-ldl
            -C link-arg=-Wl,--allow-shlib-undefined
            -C link-arg=-Wl,--thinlto-cache-dir=$(pwd)/target/release/lto-cache
            -C link-arg=-Wl,--thinlto-cache-policy,cache_size_bytes=700m
            -C link-arg=/tmp/memfd_create_shim.o
            --cfg tokio_unstable
            $RUSTFLAGS
          __1
          RUSTDOCFLAGS<<__1
            -C linker-plugin-lto=true
            -C linker=clang-19
            -C link-arg=-fuse-ld=lld-19
            -C link-arg=-ldl
            -C link-arg=-Wl,--allow-shlib-undefined
            -C link-arg=-Wl,--thinlto-cache-dir=$(pwd)/target/release/lto-cache
            -C link-arg=-Wl,--thinlto-cache-policy,cache_size_bytes=700m
            -C link-arg=/tmp/memfd_create_shim.o
            --cfg tokio_unstable
            $RUSTFLAGS
          __1
          CC=/usr/bin/clang-19
          CFLAGS=$CFLAGS
          " > $GITHUB_ENV
      - name: Remove macOS cURL --ipv4 flag
        run: |-
          curl --version
          which curl
          cat /etc/hosts
          rm ~/.curlrc || true
        if: '!(matrix.skip) && (matrix.os == ''macos'')'
      - name: Install macOS aarch64 lld
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: ./tools/install_prebuilt.js ld64.lld
        if: '!(matrix.skip) && (matrix.os == ''macos'' && matrix.arch == ''aarch64'')'
      - name: Install rust-codesign
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: |-
          ./tools/install_prebuilt.js rcodesign
          echo $GITHUB_WORKSPACE/third_party/prebuilt/mac >> $GITHUB_PATH
        if: '!(matrix.skip) && (matrix.os == ''macos'')'
      - name: Log versions
        run: |-
          echo '*** Python'
          command -v python && python --version || echo 'No python found or bad executable'
          echo '*** Rust'
          command -v rustc && rustc --version || echo 'No rustc found or bad executable'
          echo '*** Cargo'
          command -v cargo && cargo --version || echo 'No cargo found or bad executable'
          echo '*** Deno'
          command -v deno && deno --version || echo 'No deno found or bad executable'
          echo '*** Node'
          command -v node && node --version || echo 'No node found or bad executable'
          echo '*** Installed packages'
          command -v dpkg && dpkg -l || echo 'No dpkg found or bad executable'
        if: '!(matrix.skip)'
      - name: Install benchmark tools
        if: '!(matrix.skip) && (matrix.job == ''bench'')'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: ./tools/install_prebuilt.js wrk hyperfine
      - name: Restore cache build output (PR)
        uses: actions/cache/restore@v4
        if: '!(matrix.skip) && (github.ref != ''refs/heads/main'' && !startsWith(github.ref, ''refs/tags/''))'
        with:
          path: |-
            ./target
            !./target/*/gn_out
            !./target/*/gn_root
            !./target/*/*.zip
            !./target/*/*.tar.gz
          key: never_saved
          restore-keys: '59-cargo-target-${{ matrix.os }}-${{ matrix.arch }}-${{ matrix.profile }}-${{ matrix.job }}-'
      - name: Apply and update mtime cache
        if: '!(matrix.skip) && (!startsWith(github.ref, ''refs/tags/''))'
        uses: ./.github/mtime_cache
        with:
          cache-path: ./target
      - name: Set up playwright cache
        uses: actions/cache@v4
        with:
          path: ./.ms-playwright
          key: 'playwright-${{ runner.os }}-${{ runner.arch }}'
        if: '!(matrix.skip)'
      - name: test_format.js
        if: '!(matrix.skip) && (matrix.job == ''lint'' && matrix.os == ''linux'')'
        run: deno run --allow-write --allow-read --allow-run --allow-net ./tools/format.js --check
      - name: Lint PR title
        if: '!(matrix.skip) && (matrix.job == ''lint'' && github.event_name == ''pull_request'' && matrix.os == ''linux'')'
        env:
          PR_TITLE: '${{ github.event.pull_request.title }}'
        run: deno run ./tools/verify_pr_title.js "$PR_TITLE"
      - name: lint.js
        if: '!(matrix.skip) && (matrix.job == ''lint'')'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: deno run --allow-write --allow-read --allow-run --allow-net --allow-env ./tools/lint.js
      - name: jsdoc_checker.js
        if: '!(matrix.skip) && (matrix.job == ''lint'')'
        run: deno run --allow-read --allow-env --allow-sys ./tools/jsdoc_checker.js
      - name: node_compat/setup.ts --check
        if: '!(matrix.skip) && (matrix.job == ''lint'' && matrix.os == ''linux'')'
        run: deno run --allow-write --allow-read --allow-run=git ./tests/node_compat/runner/setup.ts --check
      - name: Check tracing build
        if: '!(matrix.skip) && (matrix.job == ''test'' && matrix.profile == ''debug'' && matrix.os == ''linux'' && matrix.arch == ''x86_64'')'
        run: cargo check -p deno --features=lsp-tracing
        env:
          CARGO_PROFILE_DEV_DEBUG: 0
      - name: Build debug
        if: '!(matrix.skip) && (matrix.job == ''test'' && matrix.profile == ''debug'')'
        run: cargo build --locked --all-targets --features=panic-trace
        env:
          CARGO_PROFILE_DEV_DEBUG: 0
      - name: Build release
        if: |-
          !(matrix.skip) && ((matrix.job == 'test' || matrix.job == 'bench') &&
          matrix.profile == 'release' && (matrix.use_sysroot ||
          github.repository == 'denoland/deno'))
        run: |-
          df -h
          cargo build --release --locked --all-targets --features=panic-trace
          df -h
      - name: Check deno binary
        if: '!(matrix.skip) && (matrix.job == ''test'')'
        run: 'target/${{ matrix.profile }}/deno eval "console.log(1+2)" | grep 3'
        env:
          NO_COLOR: 1
      - name: Check deno binary (in sysroot)
        if: '!(matrix.skip) && (matrix.job == ''test'' && matrix.use_sysroot)'
        run: 'sudo chroot /sysroot "$(pwd)/target/${{ matrix.profile }}/deno" --version'
      - name: Generate symcache
        if: |-
          !(matrix.skip) && ((matrix.job == 'test' || matrix.job == 'bench') &&
          matrix.profile == 'release' && (matrix.use_sysroot ||
          github.repository == 'denoland/deno'))
        run: |-
          target/release/deno -A tools/release/create_symcache.ts ./deno.symcache
          du -h deno.symcache
          du -h target/release/deno
        env:
          NO_COLOR: 1
      - name: Upload PR artifact (linux)
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'release' && (matrix.use_sysroot ||
          (github.repository == 'denoland/deno' &&
          (github.ref == 'refs/heads/main' ||
          startsWith(github.ref, 'refs/tags/')))))
        uses: actions/upload-artifact@v4
        with:
          name: 'deno-${{ matrix.os }}-${{ matrix.arch }}-${{ github.event.number }}'
          path: target/release/deno
      - name: Pre-release (linux)
        if: |-
          !(matrix.skip) && (matrix.os == 'linux' &&
          (matrix.job == 'test' || matrix.job == 'bench') &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno')
        run: |-
          cd target/release
          ./deno -A ../../tools/release/create_symcache.ts deno-${{ matrix.arch }}-unknown-linux-gnu.symcache
          strip ./deno
          zip -r deno-${{ matrix.arch }}-unknown-linux-gnu.zip deno
          shasum -a 256 deno-${{ matrix.arch }}-unknown-linux-gnu.zip > deno-${{ matrix.arch }}-unknown-linux-gnu.zip.sha256sum
          strip ./denort
          zip -r denort-${{ matrix.arch }}-unknown-linux-gnu.zip denort
          shasum -a 256 denort-${{ matrix.arch }}-unknown-linux-gnu.zip > denort-${{ matrix.arch }}-unknown-linux-gnu.zip.sha256sum
          ./deno types > lib.deno.d.ts
      - name: Pre-release (mac)
        if: |-
          !(matrix.skip) && (matrix.os == 'macos' &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno')
        env:
          APPLE_CODESIGN_KEY: '${{ secrets.APPLE_CODESIGN_KEY }}'
          APPLE_CODESIGN_PASSWORD: '${{ secrets.APPLE_CODESIGN_PASSWORD }}'
        run: |-
          target/release/deno -A tools/release/create_symcache.ts target/release/deno-${{ matrix.arch }}-apple-darwin.symcache
          strip -x -S target/release/deno
          echo "Key is $(echo $APPLE_CODESIGN_KEY | base64 -d | wc -c) bytes"
          rcodesign sign target/release/deno --code-signature-flags=runtime --p12-password="$APPLE_CODESIGN_PASSWORD" --p12-file=<(echo $APPLE_CODESIGN_KEY | base64 -d) --entitlements-xml-file=cli/entitlements.plist
          cd target/release
          zip -r deno-${{ matrix.arch }}-apple-darwin.zip deno
          shasum -a 256 deno-${{ matrix.arch }}-apple-darwin.zip > deno-${{ matrix.arch }}-apple-darwin.zip.sha256sum
          strip -x -S ./denort
          zip -r denort-${{ matrix.arch }}-apple-darwin.zip denort
          shasum -a 256 denort-${{ matrix.arch }}-apple-darwin.zip > denort-${{ matrix.arch }}-apple-darwin.zip.sha256sum
      - name: Authenticate with Azure (windows)
        if: |-
          !(matrix.skip) && (matrix.os == 'windows' &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/')))
        uses: azure/login@v1
        with:
          client-id: '${{ secrets.AZURE_CLIENT_ID }}'
          tenant-id: '${{ secrets.AZURE_TENANT_ID }}'
          subscription-id: '${{ secrets.AZURE_SUBSCRIPTION_ID }}'
          enable-AzPSSession: true
      - name: Code sign deno.exe (windows)
        if: |-
          !(matrix.skip) && (matrix.os == 'windows' &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/')))
        uses: azure/trusted-signing-action@v0
        with:
          endpoint: 'https://eus.codesigning.azure.net/'
          trusted-signing-account-name: deno-cli-code-signing
          certificate-profile-name: deno-cli-code-signing-cert
          files-folder: target/release
          files-folder-filter: deno.exe
          file-digest: SHA256
          timestamp-rfc3161: 'http://timestamp.acs.microsoft.com'
          timestamp-digest: SHA256
          exclude-environment-credential: true
          exclude-workload-identity-credential: true
          exclude-managed-identity-credential: true
          exclude-shared-token-cache-credential: true
          exclude-visual-studio-credential: true
          exclude-visual-studio-code-credential: true
          exclude-azure-cli-credential: false
      - name: Verify signature (windows)
        if: |-
          !(matrix.skip) && (matrix.os == 'windows' &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/')))
        shell: pwsh
        run: |-
          $SignTool = Get-ChildItem -Path "C:\Program Files*\Windows Kits\*\bin\*\x64\signtool.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
          $SignToolPath = $SignTool.FullName
          & $SignToolPath verify /pa /v target\release\deno.exe
      - name: Pre-release (windows)
        if: |-
          !(matrix.skip) && (matrix.os == 'windows' &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno')
        shell: pwsh
        run: |-
          Compress-Archive -CompressionLevel Optimal -Force -Path target/release/deno.exe -DestinationPath target/release/deno-${{ matrix.arch }}-pc-windows-msvc.zip
          Get-FileHash target/release/deno-${{ matrix.arch }}-pc-windows-msvc.zip -Algorithm SHA256 | Format-List > target/release/deno-${{ matrix.arch }}-pc-windows-msvc.zip.sha256sum
          Compress-Archive -CompressionLevel Optimal -Force -Path target/release/denort.exe -DestinationPath target/release/denort-${{ matrix.arch }}-pc-windows-msvc.zip
          Get-FileHash target/release/denort-${{ matrix.arch }}-pc-windows-msvc.zip -Algorithm SHA256 | Format-List > target/release/denort-${{ matrix.arch }}-pc-windows-msvc.zip.sha256sum
          target/release/deno.exe -A tools/release/create_symcache.ts target/release/deno-${{ matrix.arch }}-pc-windows-msvc.symcache
      - name: Upload canary to dl.deno.land
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          github.ref == 'refs/heads/main')
        run: |-
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.zip gs://dl.deno.land/canary/$(git rev-parse HEAD)/
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.sha256sum gs://dl.deno.land/canary/$(git rev-parse HEAD)/
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.symcache gs://dl.deno.land/canary/$(git rev-parse HEAD)/
          echo ${{ github.sha }} > canary-latest.txt
          gsutil -h "Cache-Control: no-cache" cp canary-latest.txt gs://dl.deno.land/canary-$(rustc -vV | sed -n "s|host: ||p")-latest.txt
          rm canary-latest.txt gha-creds-*.json
      - name: Autobahn testsuite
        if: |-
          !(matrix.skip) && ((matrix.os == 'linux' && matrix.arch != 'aarch64') &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          !startsWith(github.ref, 'refs/tags/'))
        run: target/release/deno run -A --config tests/config/deno.json ext/websocket/autobahn/fuzzingclient.js
      - name: 'Test (full, debug)'
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'debug' &&
          !startsWith(github.ref, 'refs/tags/') &&
          matrix.os == 'linux')
        run: cargo test --locked --features=panic-trace
        env:
          CARGO_PROFILE_DEV_DEBUG: 0
      - name: 'Test (fast, debug)'
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'debug' &&
          (startsWith(github.ref, 'refs/tags/') || matrix.os != 'linux'))
        run: |-
          cargo test --locked --lib --features=panic-trace
          cargo test --locked --tests --features=panic-trace
        env:
          CARGO_PROFILE_DEV_DEBUG: 0
      - name: Test (release)
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'release' &&
          (matrix.use_sysroot || (
          github.repository == 'denoland/deno' &&
          !startsWith(github.ref, 'refs/tags/'))))
        run: cargo test --release --locked --features=panic-trace
      - name: Ensure no git changes
        if: '!(matrix.skip) && (matrix.job == ''test'' && github.event_name == ''pull_request'')'
        run: "if [[ -n \"$(git status --porcelain)\" ]]; then\necho \"❌ Git working directory is dirty. Ensure `cargo test` is not modifying git tracked files.\"\necho \"\"\necho \"\U0001F4CB Status:\"\ngit status\necho \"\"\nexit 1\nfi"
      - name: Configure hosts file for WPT
        if: '!(matrix.skip) && (matrix.wpt)'
        run: ./wpt make-hosts-file | sudo tee -a /etc/hosts
        working-directory: tests/wpt/suite/
      - name: Run web platform tests (debug)
        if: '!(matrix.skip) && (matrix.wpt && matrix.profile == ''debug'')'
        env:
          DENO_BIN: ./target/debug/deno
        run: |-
          deno run -RWNE --allow-run --lock=tools/deno.lock.json --config tests/config/deno.json \
              ./tests/wpt/wpt.ts setup
          deno run -RWNE --allow-run --lock=tools/deno.lock.json --config tests/config/deno.json --unsafely-ignore-certificate-errors \
              ./tests/wpt/wpt.ts run --quiet --binary="$DENO_BIN"
      - name: Run web platform tests (release)
        if: '!(matrix.skip) && (matrix.wpt && matrix.profile == ''release'')'
        env:
          DENO_BIN: ./target/release/deno
        run: |-
          deno run -RWNE --allow-run --lock=tools/deno.lock.json --config tests/config/deno.json \
              ./tests/wpt/wpt.ts setup
          deno run -RWNE --allow-run --lock=tools/deno.lock.json --config tests/config/deno.json --unsafely-ignore-certificate-errors \
              ./tests/wpt/wpt.ts run --quiet --release --binary="$DENO_BIN" --json=wpt.json --wptreport=wptreport.json
      - name: Upload wpt results to dl.deno.land
        continue-on-error: true
        if: |-
          !(matrix.skip) && (matrix.wpt &&
          matrix.os == 'linux' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          github.ref == 'refs/heads/main' && !startsWith(github.ref, 'refs/tags/'))
        run: |-
          gzip ./wptreport.json
          gsutil -h "Cache-Control: public, max-age=3600" cp ./wpt.json gs://dl.deno.land/wpt/$(git rev-parse HEAD).json
          gsutil -h "Cache-Control: public, max-age=3600" cp ./wptreport.json.gz gs://dl.deno.land/wpt/$(git rev-parse HEAD)-wptreport.json.gz
          echo $(git rev-parse HEAD) > wpt-latest.txt
          gsutil -h "Cache-Control: no-cache" cp wpt-latest.txt gs://dl.deno.land/wpt-latest.txt
      - name: Upload wpt results to wpt.fyi
        continue-on-error: true
        if: |-
          !(matrix.skip) && (matrix.wpt &&
          matrix.os == 'linux' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          github.ref == 'refs/heads/main' && !startsWith(github.ref, 'refs/tags/'))
        env:
          WPT_FYI_USER: deno
          WPT_FYI_PW: '${{ secrets.WPT_FYI_PW }}'
          GITHUB_TOKEN: '${{ secrets.DENOBOT_PAT }}'
        run: |-
          ./target/release/deno run --allow-all --lock=tools/deno.lock.json \
              ./tools/upload_wptfyi.js $(git rev-parse HEAD) --ghstatus
      - name: Run benchmarks
        if: '!(matrix.skip) && (matrix.job == ''bench'' && !startsWith(github.ref, ''refs/tags/''))'
        run: cargo bench --locked
      - name: Post Benchmarks
        if: |-
          !(matrix.skip) && (matrix.job == 'bench' &&
          github.repository == 'denoland/deno' &&
          github.ref == 'refs/heads/main' && !startsWith(github.ref, 'refs/tags/'))
        env:
          DENOBOT_PAT: '${{ secrets.DENOBOT_PAT }}'
        run: |-
          git clone --depth 1 --branch gh-pages                             \
              https://${DENOBOT_PAT}@github.com/denoland/benchmark_data.git \
              gh-pages
          ./target/release/deno run --allow-all ./tools/build_benchmark_jsons.js --release
          cd gh-pages
          git config user.email "<EMAIL>"
          git config user.name "denobot"
          git add .
          git commit --message "Update benchmarks"
          git push origin gh-pages
      - name: Build product size info
        if: '!(matrix.skip) && (matrix.job != ''lint'' && matrix.profile != ''debug'' && github.repository == ''denoland/deno'' && (github.ref == ''refs/heads/main'' || startsWith(github.ref, ''refs/tags/'')))'
        run: |-
          du -hd1 "./target/${{ matrix.profile }}"
          du -ha  "./target/${{ matrix.profile }}/deno"
          du -ha  "./target/${{ matrix.profile }}/denort"
      - name: Worker info
        if: '!(matrix.skip) && (matrix.job == ''bench'')'
        run: |-
          cat /proc/cpuinfo
          cat /proc/meminfo
      - name: Upload release to dl.deno.land (unix)
        if: |-
          !(matrix.skip) && (matrix.os != 'windows' &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          startsWith(github.ref, 'refs/tags/'))
        run: |-
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.zip gs://dl.deno.land/release/${GITHUB_REF#refs/*/}/
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.sha256sum gs://dl.deno.land/release/${GITHUB_REF#refs/*/}/
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.symcache gs://dl.deno.land/release/${GITHUB_REF#refs/*/}/
      - name: Upload release to dl.deno.land (windows)
        if: |-
          !(matrix.skip) && (matrix.os == 'windows' &&
          matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          startsWith(github.ref, 'refs/tags/'))
        env:
          CLOUDSDK_PYTHON: '${{env.pythonLocation}}\python.exe'
        run: |-
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.zip gs://dl.deno.land/release/${GITHUB_REF#refs/*/}/
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.sha256sum gs://dl.deno.land/release/${GITHUB_REF#refs/*/}/
          gsutil -h "Cache-Control: public, max-age=3600" cp ./target/release/*.symcache gs://dl.deno.land/release/${GITHUB_REF#refs/*/}/
      - name: Create release notes
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          startsWith(github.ref, 'refs/tags/'))
        run: |-
          export PATH=$PATH:$(pwd)/target/release
          ./tools/release/05_create_release_notes.ts
      - name: Upload release to GitHub
        uses: softprops/action-gh-release@v0.1.15
        if: |-
          !(matrix.skip) && (matrix.job == 'test' &&
          matrix.profile == 'release' &&
          github.repository == 'denoland/deno' &&
          startsWith(github.ref, 'refs/tags/'))
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        with:
          files: |-
            target/release/deno-x86_64-pc-windows-msvc.zip
            target/release/deno-x86_64-pc-windows-msvc.zip.sha256sum
            target/release/denort-x86_64-pc-windows-msvc.zip
            target/release/denort-x86_64-pc-windows-msvc.zip.sha256sum
            target/release/deno-x86_64-unknown-linux-gnu.zip
            target/release/deno-x86_64-unknown-linux-gnu.zip.sha256sum
            target/release/denort-x86_64-unknown-linux-gnu.zip
            target/release/denort-x86_64-unknown-linux-gnu.zip.sha256sum
            target/release/deno-x86_64-apple-darwin.zip
            target/release/deno-x86_64-apple-darwin.zip.sha256sum
            target/release/denort-x86_64-apple-darwin.zip
            target/release/denort-x86_64-apple-darwin.zip.sha256sum
            target/release/deno-aarch64-unknown-linux-gnu.zip
            target/release/deno-aarch64-unknown-linux-gnu.zip.sha256sum
            target/release/denort-aarch64-unknown-linux-gnu.zip
            target/release/denort-aarch64-unknown-linux-gnu.zip.sha256sum
            target/release/deno-aarch64-apple-darwin.zip
            target/release/deno-aarch64-apple-darwin.zip.sha256sum
            target/release/denort-aarch64-apple-darwin.zip
            target/release/denort-aarch64-apple-darwin.zip.sha256sum
            target/release/deno_src.tar.gz
            target/release/lib.deno.d.ts
          body_path: target/release/release-notes.md
          draft: true
      - name: Save cache build output (main)
        uses: actions/cache/save@v4
        if: '!(matrix.skip) && ((matrix.job == ''test'' || matrix.job == ''lint'') && github.ref == ''refs/heads/main'')'
        with:
          path: |-
            ./target
            !./target/*/gn_out
            !./target/*/gn_root
            !./target/*/*.zip
            !./target/*/*.tar.gz
          key: '59-cargo-target-${{ matrix.os }}-${{ matrix.arch }}-${{ matrix.profile }}-${{ matrix.job }}-${{ github.sha }}'
  wasm:
    name: build wasm32
    needs:
      - pre_build
    if: '${{ needs.pre_build.outputs.skip_build != ''true'' }}'
    runs-on: ubuntu-24.04
    timeout-minutes: 30
    steps:
      - name: Configure git
        run: |-
          git config --global core.symlinks true
          git config --global fetch.parallel 32
        if: '!(matrix.skip)'
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 5
          submodules: false
        if: '!(matrix.skip)'
      - uses: dsherret/rust-toolchain-file@v1
        if: '!(matrix.skip)'
      - name: Install wasm target
        run: rustup target add wasm32-unknown-unknown
        if: '!(matrix.skip)'
      - name: Cargo check (deno_resolver)
        run: cargo check --target wasm32-unknown-unknown -p deno_resolver && cargo check --target wasm32-unknown-unknown -p deno_resolver --features graph && cargo check --target wasm32-unknown-unknown -p deno_resolver --features graph --features deno_ast
        if: '!(matrix.skip)'
      - name: Cargo check (deno_npm_installer)
        run: cargo check --target wasm32-unknown-unknown -p deno_npm_installer
        if: '!(matrix.skip)'
  publish-canary:
    name: publish canary
    runs-on: ubuntu-24.04
    needs:
      - build
    if: github.repository == 'denoland/deno' && github.ref == 'refs/heads/main'
    steps:
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          project_id: denoland
          credentials_json: '${{ secrets.GCP_SA_KEY }}'
          export_environment_variables: true
          create_credentials_file: true
      - name: Setup gcloud
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: denoland
      - name: Upload canary version file to dl.deno.land
        run: |-
          echo ${{ github.sha }} > canary-latest.txt
          gsutil -h "Cache-Control: no-cache" cp canary-latest.txt gs://dl.deno.land/canary-latest.txt
