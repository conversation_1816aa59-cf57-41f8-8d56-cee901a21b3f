name: version_bump

on:
  workflow_dispatch:
    inputs:
      releaseKind:
        description: 'Kind of version bump'
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - rc
        required: true

jobs:
  build:
    name: version bump
    runs-on: ubuntu-24.04
    timeout-minutes: 90

    env:
      CARGO_TERM_COLOR: always
      RUST_BACKTRACE: full
      RUSTC_FORCE_INCREMENTAL: 1

    steps:
      - name: Configure git
        run: |
          git config --global core.symlinks true
          git config --global fetch.parallel 32

      - name: Clone repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.DENOBOT_PAT }}
          submodules: recursive

      - uses: dsherret/rust-toolchain-file@v1

      - name: Install deno
        uses: denoland/setup-deno@v2
        with:
          deno-version: v2.x

      - name: Run version bump
        run: |
          git remote add upstream https://github.com/denoland/deno
          ./tools/release/01_bump_crate_versions.ts --${{github.event.inputs.releaseKind}}

      - name: Create PR
        env:
          GITHUB_TOKEN: ${{ secrets.DENOBOT_PAT }}
          GH_WORKFLOW_ACTOR: ${{ github.actor }}
        run: |
          git config user.email "${{ github.actor }}@users.noreply.github.com"
          git config user.name "${{ github.actor }}"
          ./tools/release/02_create_pr.ts
