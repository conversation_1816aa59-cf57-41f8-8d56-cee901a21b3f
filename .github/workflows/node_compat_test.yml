name: node_compat_test

on:
  schedule:
    - cron: '0 10 * * *'
  workflow_dispatch:

jobs:
  test:
    runs-on: '${{ matrix.runner }}'
    strategy:
      matrix:
        include:
          - os: linux
            runner: ubuntu-latest
          - os: windows
            runner: windows-latest
          - os: darwin
            runner: macos-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Setup Deno
        uses: denoland/setup-deno@v2
        with:
          deno-version: canary
      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          project_id: denoland
          credentials_json: '${{ secrets.GCP_SA_KEY }}'
          export_environment_variables: true
          create_credentials_file: true
      - name: Setup gcloud
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: denoland
      - name: Run tests
        run: deno -A tools/node_compat_tests.js
      - name: Gzip the report
        run: gzip tests/node_compat/report.json
      - name: Upload the report to dl.deno.land
        run: |-
          gsutil -h "Cache-Control: public, max-age=3600" cp tests/node_compat/report.json.gz gs://dl.deno.land/node-compat-test/$(date +%F)/report-${{matrix.os}}.json.gz
  summary:
    runs-on: ubuntu-latest
    needs: test
    if: ${{ always() }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Setup Deno
        uses: denoland/setup-deno@v2
      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          project_id: denoland
          credentials_json: '${{ secrets.GCP_SA_KEY }}'
          export_environment_variables: true
          create_credentials_file: true
      - name: Setup gcloud
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: denoland
      - name: Add the day summary to the month summary
        run: deno -A --config tests/config/deno.json tests/node_compat/add_day_summary_to_month_summary.ts
      - name: Gzip the month summary
        run: gzip tests/node_compat/summary.json -k
      - name: Upload the month summary
        run: |-
          gsutil -h "Cache-Control: public, max-age=3600" cp tests/node_compat/summary.json.gz gs://dl.deno.land/node-compat-test/summary-$(date +%Y-%m).json.gz
      - name: Post message to slack channel
        run: deno -A --config tests/config/deno.json tests/node_compat/slack.ts
        env:
          SLACK_TOKEN: ${{ secrets.NODE_COMPAT_SLACK_TOKEN }} # NodeCompat bot
          SLACK_CHANNEL: ${{ secrets.NODE_COMPAT_SLACK_CHANNEL }} # #node-compat channel
