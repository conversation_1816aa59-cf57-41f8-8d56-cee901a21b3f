{
  "name": "Rust",
  "build": {
    "dockerfile": "Dockerfile"
  },
  "runArgs": ["--cap-add=SYS_PTRACE", "--security-opt", "seccomp=unconfined"],
  "customizations": {
    "vscode": {
      "settings": {
        "lldb.executable": "/usr/bin/lldb",
        // VS Code don't watch files under ./target
        "files.watcherExclude": {
          "**/target/**": true
        },
        "extensions": [
          "rust-lang.rust-analyzer",
          "tamasfe.even-better-toml",
          "vadimcn.vscode-lldb",
          "mutantdino.resourcemonitor"
        ]
      }
    }
  },
  "postCreateCommand": "git submodule update --init",
  "remoteUser": "vscode"
}
